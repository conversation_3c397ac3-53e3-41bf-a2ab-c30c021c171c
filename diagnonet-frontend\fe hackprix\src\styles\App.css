/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #E61A4F 0%, #FB6E92 50%, #FFFFFF 100%);
  min-height: 100vh;
}

.App {
  min-height: 100vh;
  position: relative;
}

/* Color Theme Variables */
:root {
  /* Theme 1: Purple & White */
  --primary-purple: #800080;
  --white: #FFFFFF;
  
  /* Theme 2: Red, Pink & White */
  --primary-red: #E61A4F;
  --secondary-pink: #FB6E92;
  --pure-white: #FFFFFF;
  
  /* Typography */
  --font-family: 'Helvetica', Arial, sans-serif;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-bold: 700;
  --font-weight-black: 900;
}

/* Medical Tech Visualization Elements */
.dna-helix {
  position: absolute;
  width: 100px;
  height: 200px;
  opacity: 0.1;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 200"><path d="M20 0 Q50 25 80 50 Q50 75 20 100 Q50 125 80 150 Q50 175 20 200" stroke="%23800080" stroke-width="3" fill="none"/><path d="M80 0 Q50 25 20 50 Q50 75 80 100 Q50 125 20 150 Q50 175 80 200" stroke="%23E61A4F" stroke-width="3" fill="none"/></svg>') no-repeat center;
  background-size: contain;
}

.medical-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(224, 26, 79, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(224, 26, 79, 0.05) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.8s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Button Styles */
.btn {
  padding: 15px 30px;
  border: none;
  border-radius: 8px;
  font-family: var(--font-family);
  font-weight: var(--font-weight-bold);
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(45deg, var(--primary-red), var(--secondary-pink));
  color: var(--pure-white);
  box-shadow: 0 4px 15px rgba(224, 26, 79, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(224, 26, 79, 0.4);
}

.btn-secondary {
  background: var(--pure-white);
  color: var(--primary-red);
  border: 2px solid var(--primary-red);
}

.btn-secondary:hover {
  background: var(--primary-red);
  color: var(--pure-white);
  transform: translateY(-2px);
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: var(--font-weight-bold);
  color: #000;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  font-family: var(--font-family);
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  color: #000;
  font-weight: 500;
}

.form-input:focus {
  outline: none;
  border-color: #000;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Card Styles */
.card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(160, 32, 240, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(160, 32, 240, 0.1);
}

/* Loading Animation */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(224, 26, 79, 0.2);
  border-top: 4px solid var(--primary-red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .btn {
    padding: 12px 24px;
    font-size: 14px;
  }
  
  .card {
    padding: 20px;
    margin: 10px;
  }
}
