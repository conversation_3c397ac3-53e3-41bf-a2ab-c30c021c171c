import pandas as pd
import numpy as np
import joblib
import shap
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, StratifiedKFold, RandomizedSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, accuracy_score
from sklearn.calibration import CalibratedClassifierCV
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class VitalsAgent:
    """
    Medical diagnosis agent that analyzes vital signs to predict conditions using
    a Random Forest trained on synthetic data with CV, hyperparameter tuning,
    calibration, noise injection, and feature engineering.
    """
    def __init__(self, model_path: str = None):
        self.scaler = StandardScaler()
        self.features = ['sbp','dbp','hr','temp','rr','spo2','map','shock_index']
        self.conditions = [
            "Normal", "Hypertension", "Hypotension", "Tachycardia",
            "Bradycardia", "Fever", "Hypothermia", "Respiratory_Distress",
            "Sepsis", "Pneumonia", "Heart_Failure", "Dehydration"
        ]
        if model_path and joblib.os.path.exists(model_path):
            pkg = joblib.load(model_path)
            self.model = pkg['model']
            self.scaler = pkg['scaler']
        else:
            self._create_medical_model()

    def _generate_realistic_medical_data(self, n_samples: int) -> Tuple[np.ndarray, np.ndarray]:
        X = np.zeros((n_samples, 6))
        y = np.zeros(n_samples, dtype=int)
        # ... (same conditions_data dict as before) ...
        # Generate and inject noise/missingness
        sample_idx = 0
        for cond, params in conditions_data.items():
            n_cond = int(n_samples * params['weight'])
            for i in range(n_cond):
                if sample_idx >= n_samples: break
                row = []
                for feat in ['systolic_bp','diastolic_bp','heart_rate','temperature','respiratory_rate','oxygen_saturation']:
                    low, high, sd = params[feat]
                    val = np.random.normal(low + (high-low)*np.random.rand(), sd)
                    val = np.clip(val, sd, high)
                    # noise
                    val += np.random.normal(0, 0.05*val)
                    # missingness
                    if np.random.rand() < 0.03:
                        val = np.nan
                    row.append(val)
                X[sample_idx] = row
                y[sample_idx] = cond
                sample_idx += 1
        return X[:sample_idx], y[:sample_idx]

    def _create_medical_model(self):
        # 1. Generate data
        X_raw, y = self._generate_realistic_medical_data(5000)
        df = pd.DataFrame(X_raw, columns=['sbp','dbp','hr','temp','rr','spo2'])
        # Feature engineering
        df['map'] = (2*df['dbp'] + df['sbp'])/3
        df['shock_index'] = df['hr'] / df['sbp']
        df.fillna(df.median(), inplace=True)
        X = df.values
        # 2. Split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, stratify=y, random_state=42)
        # 3. Scale
        self.scaler.fit(X_train)
        X_train_s = self.scaler.transform(X_train)
        X_test_s  = self.scaler.transform(X_test)
        # 4. Hyperparameter tuning
        rf = RandomForestClassifier(class_weight='balanced', random_state=42)
        param_dist = {
            'n_estimators':[100,200,400], 'max_depth':[None,10,15],
            'min_samples_split':[2,5], 'min_samples_leaf':[1,2],
            'max_features':['sqrt','log2']
        }
        search = RandomizedSearchCV(
            rf, param_dist, n_iter=10, cv=3,
            scoring='roc_auc', n_jobs=-1, random_state=42)
        search.fit(X_train_s, y_train)
        best_rf = search.best_estimator_
        # 5. Calibration
        calibrator = CalibratedClassifierCV(best_rf, method='isotonic', cv=3)
        calibrator.fit(X_train_s, y_train)
        self.model = calibrator
        # 6. Evaluate
        preds = self.model.predict_proba(X_test_s)[:,1]
        print(f"Test AUC: {roc_auc_score(y_test,preds):.3f}")
        print(f"Test Acc: {accuracy_score(y_test,preds>=0.5):.3f}")
        # 7. Save
        joblib.dump({'model':self.model,'scaler':self.scaler}, 'models/vitals_rf_calibrated.joblib')

    def analyze_vitals(self, vitals: Dict[str,float]) -> Dict:
        # Build feature vector
        row = [vitals.get(k, np.nan) for k in ['sbp','dbp','hr','temp','rr','spo2']]
        # engineered
        sbp, dbp, hr, temp, rr, spo2 = row
        map_ = (2*dbp+sbp)/3
        si = hr/sbp if sbp else 0
        vec = np.array([*row, map_, si]).reshape(1,-1)
        vec = self.scaler.transform(vec)
        prob = float(self.model.predict_proba(vec)[0,1])
        pred = int(prob>=0.5)
        # SHAP
        base = self.model.base_estimator_ if hasattr(self.model,'base_estimator_') else self.model
        explainer = shap.TreeExplainer(base)
        sv = explainer.shap_values(vec)[1][0]
        fi = {f:round(v,2) for f,v in sorted(zip(self.features,sv), key=lambda x:abs(x[1]),reverse=True)[:5]}
        return {
            'primary_diagnosis': self.conditions[pred],
            'confidence':round(prob,2),
            'feature_importance':fi,
            'explanation': f"Top contributors → {', '.join(f'{k} ({v:+.2f})' for k,v in fi.items())}"
        }
