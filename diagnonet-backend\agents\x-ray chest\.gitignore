# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
cxr_env/
venv/
env/
ENV/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Machine Learning
*.pkl
*.joblib
*.h5
*.hdf5
models/
checkpoints/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Data files (optional - uncomment if you don't want to track large datasets)
# *.csv
# *.json
# *.xlsx

# Distribution files
.dist/

# Temporary files
*.tmp
*.temp 