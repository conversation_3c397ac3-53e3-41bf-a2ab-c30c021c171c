.landing-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  overflow: hidden;
}

/* Background Animations */
.medical-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(160, 32, 240, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(160, 32, 240, 0.08) 1px, transparent 1px);
  background-size: 60px 60px;
  pointer-events: none;
  z-index: 1;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* Floating DNA Helixes */
.landing-page::before {
  content: '';
  position: fixed;
  top: 12%;
  left: 6%;
  width: 140px;
  height: 280px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 140 280"><path d="M25 0 Q70 35 115 70 Q70 105 25 140 Q70 175 115 210 Q70 245 25 280" stroke="%23a020f0" stroke-width="5" fill="none" opacity="0.3"/><path d="M115 0 Q70 35 25 70 Q70 105 115 140 Q70 175 25 210 Q70 245 115 280" stroke="%23E61A4F" stroke-width="5" fill="none" opacity="0.2"/></svg>') no-repeat center;
  background-size: contain;
  pointer-events: none;
  z-index: 1;
  animation: floatDNA 12s ease-in-out infinite;
}

.landing-page::after {
  content: '';
  position: fixed;
  top: 55%;
  right: 6%;
  width: 120px;
  height: 240px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 240"><path d="M20 0 Q60 30 100 60 Q60 90 20 120 Q60 150 100 180 Q60 210 20 240" stroke="%23a020f0" stroke-width="4" fill="none" opacity="0.25"/><path d="M100 0 Q60 30 20 60 Q60 90 100 120 Q60 150 20 180 Q60 210 100 240" stroke="%23E61A4F" stroke-width="4" fill="none" opacity="0.15"/></svg>') no-repeat center;
  background-size: contain;
  pointer-events: none;
  z-index: 1;
  animation: floatDNA 15s ease-in-out infinite reverse;
}

@keyframes floatDNA {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
  25% { transform: translateY(-30px) rotate(4deg); opacity: 0.5; }
  50% { transform: translateY(-60px) rotate(0deg); opacity: 0.3; }
  75% { transform: translateY(-30px) rotate(-4deg); opacity: 0.5; }
}

.landing-content {
  text-align: center;
  z-index: 10;
  max-width: 1200px;
  padding: 0 20px;
}

.hero-section {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24px;
  padding: clamp(40px, 6vw, 80px) clamp(30px, 5vw, 60px);
  box-shadow: 0 20px 60px rgba(160, 32, 240, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(160, 32, 240, 0.1);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(160, 32, 240, 0.05), transparent);
  transition: left 1s ease;
}

.hero-section:hover::before {
  left: 100%;
}

.main-title {
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 900;
  color: #000;
  margin-bottom: 25px;
  letter-spacing: -1px;
  position: relative;
  z-index: 2;
}

.tagline {
  font-size: clamp(1.3rem, 3vw, 2rem);
  font-weight: 800;
  color: #a020f0;
  margin-bottom: 20px;
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
  z-index: 2;
}

.subtitle {
  font-size: clamp(1rem, 2.5vw, 1.3rem);
  color: #555;
  margin-bottom: 60px;
  font-weight: 500;
  line-height: 1.7;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 2;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin: 60px 0;
  position: relative;
  z-index: 2;
}

.feature-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 35px 25px;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid rgba(160, 32, 240, 0.1);
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(160, 32, 240, 0.08);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(160, 32, 240, 0.1), transparent);
  transition: left 0.6s ease;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-12px) scale(1.02);
  border-color: rgba(160, 32, 240, 0.3);
  box-shadow: 0 20px 50px rgba(160, 32, 240, 0.15);
  background: rgba(255, 255, 255, 1);
}

.feature-icon {
  margin-bottom: 25px;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.feature-icon svg {
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon svg {
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: clamp(1.2rem, 2.5vw, 1.5rem);
  font-weight: 800;
  color: #000;
  margin-bottom: 15px;
  letter-spacing: -0.3px;
  position: relative;
  z-index: 2;
}

.feature-card p {
  color: #555;
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  line-height: 1.6;
  font-weight: 500;
  position: relative;
  z-index: 2;
}

.cta-button {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  padding: 22px 50px;
  margin-top: 40px;
  border-radius: 16px;
  background: linear-gradient(135deg, #a020f0, #8a1acc);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  font-weight: 700;
  box-shadow: 0 12px 30px rgba(160, 32, 240, 0.3);
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.cta-button:hover::before {
  left: 100%;
}

.cta-button:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 18px 45px rgba(160, 32, 240, 0.4);
  background: linear-gradient(135deg, #8a1acc, #a020f0);
}

/* Medical Icons Animation */
.medical-icons {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.medical-icon {
  position: absolute;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.medical-icon.heartbeat {
  animation: heartbeat 3s ease-in-out infinite;
}

.medical-icon.stethoscope {
  animation: float 5s ease-in-out infinite;
}

.medical-icon.brain {
  animation: pulse 4s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.4; }
  50% { transform: translateY(-25px) rotate(5deg); opacity: 0.6; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); opacity: 0.4; }
  25% { transform: scale(1.15); opacity: 0.6; }
  50% { transform: scale(1); opacity: 0.4; }
  75% { transform: scale(1.08); opacity: 0.5; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.4; }
  50% { transform: scale(1.1) rotate(2deg); opacity: 0.6; }
}

/* Floating Particles */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: radial-gradient(circle, #a020f0, #E61A4F);
  border-radius: 50%;
  opacity: 0.5;
}

.particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation: particleFloat1 16s linear infinite;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation: particleFloat2 20s linear infinite;
}

.particle:nth-child(3) {
  top: 40%;
  left: 60%;
  animation: particleFloat3 18s linear infinite;
}

.particle:nth-child(4) {
  top: 80%;
  left: 20%;
  animation: particleFloat1 24s linear infinite;
}

@keyframes particleFloat1 {
  0% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.5; }
  25% { transform: translateY(-40px) translateX(30px) scale(1.3); opacity: 0.7; }
  50% { transform: translateY(-80px) translateX(-20px) scale(0.8); opacity: 0.3; }
  75% { transform: translateY(-40px) translateX(-40px) scale(1.2); opacity: 0.6; }
  100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.5; }
}

@keyframes particleFloat2 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.4; }
  33% { transform: translateY(-50px) translateX(-30px) rotate(120deg); opacity: 0.7; }
  66% { transform: translateY(-30px) translateX(40px) rotate(240deg); opacity: 0.5; }
  100% { transform: translateY(0px) translateX(0px) rotate(360deg); opacity: 0.4; }
}

@keyframes particleFloat3 {
  0% { transform: scale(1) translateY(0px); opacity: 0.3; }
  50% { transform: scale(1.8) translateY(-70px); opacity: 0.8; }
  100% { transform: scale(1) translateY(0px); opacity: 0.3; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .landing-content {
    max-width: 900px;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .landing-page {
    padding: 20px;
  }

  .hero-section {
    padding: 40px 25px;
    border-radius: 20px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    margin: 50px 0;
  }

  .feature-card {
    padding: 30px 20px;
  }

  .cta-button {
    padding: 18px 40px;
    margin-top: 35px;
  }
}

@media (max-width: 480px) {
  .landing-page {
    padding: 15px;
  }

  .hero-section {
    padding: 30px 20px;
    border-radius: 16px;
  }

  .feature-card {
    padding: 25px 18px;
    border-radius: 16px;
  }

  .cta-button {
    padding: 16px 35px;
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 360px) {
  .landing-page {
    padding: 10px;
  }

  .hero-section {
    padding: 25px 15px;
    border-radius: 12px;
  }

  .feature-card {
    padding: 20px 15px;
  }
}
