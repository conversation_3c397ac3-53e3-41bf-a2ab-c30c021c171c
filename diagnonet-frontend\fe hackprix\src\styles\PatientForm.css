.patient-form-page {
  min-height: 100vh;
  background: #ffffff;
  padding: 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* Background Animations */
.medical-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(160, 32, 240, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(160, 32, 240, 0.08) 1px, transparent 1px);
  background-size: 60px 60px;
  pointer-events: none;
  z-index: 1;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* Floating DNA Helixes */
.patient-form-page::before {
  content: '';
  position: fixed;
  top: 10%;
  left: 5%;
  width: 120px;
  height: 240px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 240"><path d="M20 0 Q60 30 100 60 Q60 90 20 120 Q60 150 100 180 Q60 210 20 240" stroke="%23a020f0" stroke-width="4" fill="none" opacity="0.3"/><path d="M100 0 Q60 30 20 60 Q60 90 100 120 Q60 150 20 180 Q60 210 100 240" stroke="%23E61A4F" stroke-width="4" fill="none" opacity="0.2"/></svg>') no-repeat center;
  background-size: contain;
  pointer-events: none;
  z-index: 1;
  animation: floatDNA 8s ease-in-out infinite;
}

.patient-form-page::after {
  content: '';
  position: fixed;
  top: 60%;
  right: 5%;
  width: 100px;
  height: 200px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 200"><path d="M15 0 Q50 25 85 50 Q50 75 15 100 Q50 125 85 150 Q50 175 15 200" stroke="%23a020f0" stroke-width="3" fill="none" opacity="0.25"/><path d="M85 0 Q50 25 15 50 Q50 75 85 100 Q50 125 15 150 Q50 175 85 200" stroke="%23E61A4F" stroke-width="3" fill="none" opacity="0.15"/></svg>') no-repeat center;
  background-size: contain;
  pointer-events: none;
  z-index: 1;
  animation: floatDNA 10s ease-in-out infinite reverse;
}

@keyframes floatDNA {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
  25% { transform: translateY(-20px) rotate(5deg); opacity: 0.5; }
  50% { transform: translateY(-40px) rotate(0deg); opacity: 0.3; }
  75% { transform: translateY(-20px) rotate(-5deg); opacity: 0.5; }
}

/* Floating Particles */
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #a020f0, #E61A4F);
  border-radius: 50%;
  opacity: 0.6;
}

.particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation: particleFloat1 12s linear infinite;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation: particleFloat2 15s linear infinite;
}

.particle:nth-child(3) {
  top: 40%;
  left: 60%;
  animation: particleFloat3 18s linear infinite;
}

.particle:nth-child(4) {
  top: 80%;
  left: 20%;
  animation: particleFloat1 20s linear infinite;
}

.particle:nth-child(5) {
  top: 15%;
  left: 70%;
  animation: particleFloat2 14s linear infinite;
}

@keyframes particleFloat1 {
  0% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.6; }
  25% { transform: translateY(-30px) translateX(20px) scale(1.2); opacity: 0.8; }
  50% { transform: translateY(-60px) translateX(-10px) scale(0.8); opacity: 0.4; }
  75% { transform: translateY(-30px) translateX(-30px) scale(1.1); opacity: 0.7; }
  100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.6; }
}

@keyframes particleFloat2 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.5; }
  33% { transform: translateY(-40px) translateX(-20px) rotate(120deg); opacity: 0.8; }
  66% { transform: translateY(-20px) translateX(30px) rotate(240deg); opacity: 0.6; }
  100% { transform: translateY(0px) translateX(0px) rotate(360deg); opacity: 0.5; }
}

@keyframes particleFloat3 {
  0% { transform: scale(1) translateY(0px); opacity: 0.4; }
  50% { transform: scale(1.5) translateY(-50px); opacity: 0.8; }
  100% { transform: scale(1) translateY(0px); opacity: 0.4; }
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  z-index: 10;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-header {
  position: relative;
  margin-bottom: 30px;
  padding: 20px 0;
}

.back-btn {
  position: absolute;
  top: 0;
  left: 0;
  padding: 12px 24px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(160, 32, 240, 0.2);
  border-radius: 12px;
  color: #000;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(160, 32, 240, 0.1);
  cursor: pointer;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #a020f0;
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(160, 32, 240, 0.2);
  color: #a020f0;
}

.form-title {
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 900;
  color: #000;
  margin: 60px 0 15px 0;
  text-align: center;
  letter-spacing: -0.5px;
}

.form-subtitle {
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  color: #333;
  font-weight: 500;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.5;
}

.patient-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
  flex: 1;
}

/* Glassmorphism Card Styles */
.card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(160, 32, 240, 0.1);
  border-radius: 20px;
  padding: clamp(20px, 4vw, 40px);
  box-shadow: 0 8px 32px rgba(160, 32, 240, 0.08);
  transition: none;
  position: relative;
}

/* Section Styles */
.section-title {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 800;
  color: #000;
  margin-bottom: 20px;
  letter-spacing: -0.5px;
}

.section-description {
  color: #444;
  margin-bottom: 25px;
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  line-height: 1.6;
  font-weight: 500;
}

/* Form Layout */
.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 700;
  color: #000;
  font-size: clamp(0.85rem, 1.5vw, 1rem);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid rgba(160, 32, 240, 0.15);
  border-radius: 12px;
  font-family: var(--font-family);
  font-size: clamp(0.9rem, 2vw, 1rem);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  color: #000;
  font-weight: 500;
}

.form-input:focus {
  outline: none;
  border-color: #a020f0;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 3px rgba(160, 32, 240, 0.1);
}

.form-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
  font-weight: 400;
}

select.form-input {
  cursor: pointer;
}

select.form-input option {
  background: rgba(255, 255, 255, 0.95);
  color: #000;
  font-weight: 500;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
}

.form-range {
  width: 100%;
  margin: 15px 0;
  height: 6px;
  border-radius: 3px;
  background: rgba(160, 32, 240, 0.2);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #a020f0;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(160, 32, 240, 0.3);
  transition: all 0.3s ease;
}

.form-range::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(160, 32, 240, 0.4);
}

.form-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #a020f0;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(160, 32, 240, 0.3);
}

.range-value {
  display: inline-block;
  margin-left: 15px;
  font-weight: 800;
  color: #a020f0;
  font-size: clamp(1rem, 2vw, 1.2rem);
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 14px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(160, 32, 240, 0.2);
  box-shadow: 0 2px 8px rgba(160, 32, 240, 0.1);
}

.file-input {
  padding: 20px;
  border: 2px dashed rgba(160, 32, 240, 0.3);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.file-input:hover {
  border-color: #a020f0;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 16px rgba(160, 32, 240, 0.1);
}

/* Agent Selection */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.agent-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(160, 32, 240, 0.15);
  border-radius: 20px;
  padding: clamp(25px, 4vw, 35px);
  text-align: center;
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(160, 32, 240, 0.08);
}

.agent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(160, 32, 240, 0.1), transparent);
  transition: left 0.6s ease;
}

.agent-card:hover::before {
  left: 100%;
}

.agent-card:hover {
  transform: translateY(-12px) scale(1.02);
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(160, 32, 240, 0.4);
  box-shadow: 0 20px 60px rgba(160, 32, 240, 0.15);
}

.agent-card.selected {
  border-color: #a020f0;
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 60px rgba(160, 32, 240, 0.2);
}

.agent-card.selected::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid #a020f0;
  border-radius: 18px;
  animation: selectedPulse 2s ease-in-out infinite;
}

@keyframes selectedPulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.02); }
}

.agent-icon {
  margin-bottom: 25px;
  display: flex;
  justify-content: center;
  color: #000;
  opacity: 0.8;
}

.agent-card.selected .agent-icon {
  color: #000;
  opacity: 1;
  animation: pulse 2s infinite;
}

.agent-card:hover .agent-icon {
  opacity: 1;
}

.vital-icon {
  color: #E61A4F;
}

.symptom-icon {
  color: #FB6E92;
}

.xray-icon {
  color: #800080;
}

.agent-card h3 {
  font-size: clamp(1.2rem, 2.5vw, 1.5rem);
  font-weight: 800;
  color: #000;
  margin-bottom: 15px;
  letter-spacing: -0.5px;
}

.agent-card p {
  color: #333;
  font-size: clamp(0.9rem, 2vw, 1rem);
  line-height: 1.6;
  font-weight: 500;
}

/* Agent Input Sections */
.agent-inputs-section {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.agent-input-card {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.agent-input-title {
  font-size: clamp(1.3rem, 2.5vw, 1.6rem);
  font-weight: 800;
  color: #000;
  margin-bottom: 25px;
  letter-spacing: -0.5px;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 15px;
}

/* Vital Signs Styles */
.vital-inputs {
  padding: 10px 0;
}

.vital-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.vital-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.vital-label {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.label-text {
  font-size: clamp(0.9rem, 1.5vw, 1rem);
  font-weight: 700;
  color: #000;
  letter-spacing: 0.3px;
}

.label-unit {
  font-size: clamp(0.8rem, 1.2vw, 0.85rem);
  font-weight: 500;
  color: #a020f0;
  opacity: 0.8;
}

.vital-input {
  width: 100%;
  padding: 14px 18px;
  border: 2px solid rgba(160, 32, 240, 0.15);
  border-radius: 12px;
  font-family: var(--font-family);
  font-size: clamp(0.95rem, 2vw, 1.1rem);
  font-weight: 600;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  color: #000;
  text-align: center;
}

.vital-input:focus {
  outline: none;
  border-color: #a020f0;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 3px rgba(160, 32, 240, 0.1);
  transform: translateY(-1px);
}

.vital-input::placeholder {
  color: rgba(160, 32, 240, 0.5);
  font-weight: 500;
}

/* Symptom Input Styles */
.symptom-inputs {
  padding: 10px 0;
}

.symptom-input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.symptom-label {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.symptom-label .label-text {
  font-size: clamp(1rem, 2vw, 1.2rem);
  font-weight: 700;
  color: #000;
  letter-spacing: 0.3px;
}

.label-description {
  font-size: clamp(0.85rem, 1.5vw, 0.95rem);
  font-weight: 500;
  color: #666;
  line-height: 1.4;
}

.symptom-textarea {
  width: 100%;
  padding: 20px;
  border: 2px solid rgba(160, 32, 240, 0.15);
  border-radius: 16px;
  font-family: var(--font-family);
  font-size: clamp(0.95rem, 2vw, 1rem);
  font-weight: 500;
  line-height: 1.6;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  color: #000;
  resize: vertical;
  min-height: 150px;
}

.symptom-textarea:focus {
  outline: none;
  border-color: #a020f0;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 3px rgba(160, 32, 240, 0.1);
  transform: translateY(-2px);
}

.symptom-textarea::placeholder {
  color: rgba(160, 32, 240, 0.4);
  font-weight: 400;
  line-height: 1.5;
}

.character-count {
  text-align: right;
  font-size: clamp(0.8rem, 1.2vw, 0.85rem);
  color: #a020f0;
  font-weight: 500;
  opacity: 0.7;
  margin-top: 5px;
}

/* X-ray Upload Styles */
.xray-inputs {
  padding: 10px 0;
}

.xray-upload-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.xray-label {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.xray-label .label-text {
  font-size: clamp(1rem, 2vw, 1.2rem);
  font-weight: 700;
  color: #000;
  letter-spacing: 0.3px;
}

.file-upload-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.file-input-hidden {
  display: none;
}

.file-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 3px dashed rgba(160, 32, 240, 0.3);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  gap: 20px;
}

.file-upload-area:hover {
  border-color: #a020f0;
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(160, 32, 240, 0.15);
}

.upload-icon {
  color: #a020f0;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.file-upload-area:hover .upload-icon {
  opacity: 1;
  transform: scale(1.1);
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-primary {
  font-size: clamp(1rem, 2vw, 1.1rem);
  font-weight: 600;
  color: #000;
}

.upload-secondary {
  font-size: clamp(0.85rem, 1.5vw, 0.9rem);
  font-weight: 500;
  color: #666;
}

.file-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: rgba(160, 32, 240, 0.05);
  border: 1px solid rgba(160, 32, 240, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: clamp(0.9rem, 1.5vw, 1rem);
  font-weight: 600;
  color: #000;
}

.file-size {
  font-size: clamp(0.8rem, 1.2vw, 0.85rem);
  font-weight: 500;
  color: #a020f0;
}

.remove-file-btn {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: rgba(160, 32, 240, 0.1);
  color: #a020f0;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-file-btn:hover {
  background: #a020f0;
  color: white;
  transform: scale(1.1);
}

/* Submit Section */
.submit-section {
  text-align: center;
  margin-top: 40px;
  padding: 20px;
}

.submit-btn {
  font-size: clamp(1rem, 2vw, 1.2rem);
  padding: 18px 40px;
  border-radius: 16px;
  font-weight: 700;
  min-width: 200px;
  background: linear-gradient(135deg, #a020f0, #8a1acc);
  color: #fff;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(160, 32, 240, 0.3);
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-btn:hover:not(:disabled)::before {
  left: 100%;
}

.submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #8a1acc, #a020f0);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 50px rgba(160, 32, 240, 0.4);
}

.submit-btn:disabled {
  background: rgba(160, 32, 240, 0.3);
  color: rgba(255, 255, 255, 0.6);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 16px rgba(160, 32, 240, 0.1);
}

.error-message {
  color: #d32f2f;
  font-weight: 600;
  margin-top: 15px;
  font-size: clamp(0.9rem, 2vw, 1rem);
  background: rgba(255, 255, 255, 0.8);
  padding: 10px 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  display: inline-block;
}

/* Loading State */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
  background: #ffffff;
  position: relative;
}

.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(160, 32, 240, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(160, 32, 240, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 20s linear infinite;
  pointer-events: none;
}

.loading h2 {
  color: #000;
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 800;
  margin-top: 20px;
  text-align: center;
  z-index: 2;
  position: relative;
}

.loading p {
  color: #333;
  font-size: clamp(1rem, 2vw, 1.2rem);
  font-weight: 500;
  margin-top: 10px;
  text-align: center;
  z-index: 2;
  position: relative;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(160, 32, 240, 0.2);
  border-top: 4px solid #a020f0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 2;
  position: relative;
  box-shadow: 0 4px 20px rgba(160, 32, 240, 0.2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .form-container {
    max-width: 900px;
  }

  .agents-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .patient-form-page {
    padding: 15px;
  }

  .form-header {
    margin-bottom: 25px;
  }

  .back-btn {
    position: relative;
    margin-bottom: 20px;
    width: 100%;
    text-align: center;
  }

  .form-title {
    margin-top: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .agents-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .agent-card {
    padding: 25px 20px;
  }

  .submit-section {
    margin-top: 30px;
  }

  /* Responsive Vital Signs */
  .vital-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }

  .vital-input {
    padding: 12px 16px;
  }

  /* Responsive Symptom Input */
  .symptom-textarea {
    padding: 16px;
    min-height: 120px;
  }

  /* Responsive X-ray Upload */
  .file-upload-area {
    padding: 30px 15px;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .patient-form-page {
    padding: 10px;
  }

  .card {
    padding: 20px 15px;
    border-radius: 16px;
  }

  .agent-card {
    padding: 20px 15px;
  }

  .submit-btn {
    width: 100%;
    max-width: 300px;
    padding: 16px 30px;
  }

  .form-input {
    padding: 14px 16px;
  }

  /* Mobile Vital Signs */
  .vital-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .vital-input {
    padding: 12px 14px;
    font-size: 1rem;
  }

  /* Mobile Symptom Input */
  .symptom-textarea {
    padding: 14px;
    min-height: 100px;
    font-size: 0.95rem;
  }

  /* Mobile X-ray Upload */
  .file-upload-area {
    padding: 25px 12px;
    gap: 12px;
  }

  .upload-icon svg {
    width: 36px;
    height: 36px;
  }

  .file-preview {
    padding: 12px 15px;
  }
}

@media (max-width: 360px) {
  .patient-form-page {
    padding: 8px;
  }

  .form-container {
    padding: 0;
  }

  .card {
    padding: 16px 12px;
    border-radius: 12px;
  }

  .agent-card {
    padding: 16px 12px;
  }

  /* Extra Small Mobile Vital Signs */
  .vital-input {
    padding: 10px 12px;
    font-size: 0.95rem;
  }

  .label-text {
    font-size: 0.9rem;
  }

  .label-unit {
    font-size: 0.8rem;
  }

  /* Extra Small Mobile Symptom Input */
  .symptom-textarea {
    padding: 12px;
    font-size: 0.9rem;
  }

  /* Extra Small Mobile X-ray Upload */
  .file-upload-area {
    padding: 20px 10px;
  }

  .upload-icon svg {
    width: 32px;
    height: 32px;
  }
}
