.supervisor-results-page {
  min-height: 100vh;
  background: #ffffff;
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* Background Animations */
.medical-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(160, 32, 240, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(160, 32, 240, 0.08) 1px, transparent 1px);
  background-size: 60px 60px;
  pointer-events: none;
  z-index: 1;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* Floating DNA Helixes */
.supervisor-results-page::before {
  content: '';
  position: fixed;
  top: 15%;
  left: 8%;
  width: 100px;
  height: 200px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 200"><path d="M15 0 Q50 25 85 50 Q50 75 15 100 Q50 125 85 150 Q50 175 15 200" stroke="%23a020f0" stroke-width="3" fill="none" opacity="0.25"/><path d="M85 0 Q50 25 15 50 Q50 75 85 100 Q50 125 15 150 Q50 175 85 200" stroke="%23E61A4F" stroke-width="3" fill="none" opacity="0.15"/></svg>') no-repeat center;
  background-size: contain;
  pointer-events: none;
  z-index: 1;
  animation: floatDNA 10s ease-in-out infinite;
}

.supervisor-results-page::after {
  content: '';
  position: fixed;
  top: 65%;
  right: 8%;
  width: 120px;
  height: 240px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 240"><path d="M20 0 Q60 30 100 60 Q60 90 20 120 Q60 150 100 180 Q60 210 20 240" stroke="%23a020f0" stroke-width="4" fill="none" opacity="0.3"/><path d="M100 0 Q60 30 20 60 Q60 90 100 120 Q60 150 20 180 Q60 210 100 240" stroke="%23E61A4F" stroke-width="4" fill="none" opacity="0.2"/></svg>') no-repeat center;
  background-size: contain;
  pointer-events: none;
  z-index: 1;
  animation: floatDNA 12s ease-in-out infinite reverse;
}

@keyframes floatDNA {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
  25% { transform: translateY(-25px) rotate(3deg); opacity: 0.5; }
  50% { transform: translateY(-50px) rotate(0deg); opacity: 0.3; }
  75% { transform: translateY(-25px) rotate(-3deg); opacity: 0.5; }
}

/* Floating Particles */
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #a020f0, #E61A4F);
  border-radius: 50%;
  opacity: 0.6;
}

.particle:nth-child(1) {
  top: 25%;
  left: 15%;
  animation: particleFloat1 14s linear infinite;
}

.particle:nth-child(2) {
  top: 70%;
  left: 85%;
  animation: particleFloat2 18s linear infinite;
}

.particle:nth-child(3) {
  top: 45%;
  left: 65%;
  animation: particleFloat3 16s linear infinite;
}

.particle:nth-child(4) {
  top: 85%;
  left: 25%;
  animation: particleFloat1 22s linear infinite;
}

.particle:nth-child(5) {
  top: 20%;
  left: 75%;
  animation: particleFloat2 12s linear infinite;
}

@keyframes particleFloat1 {
  0% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.6; }
  25% { transform: translateY(-35px) translateX(25px) scale(1.3); opacity: 0.8; }
  50% { transform: translateY(-70px) translateX(-15px) scale(0.7); opacity: 0.4; }
  75% { transform: translateY(-35px) translateX(-35px) scale(1.2); opacity: 0.7; }
  100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.6; }
}

@keyframes particleFloat2 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.5; }
  33% { transform: translateY(-45px) translateX(-25px) rotate(120deg); opacity: 0.8; }
  66% { transform: translateY(-25px) translateX(35px) rotate(240deg); opacity: 0.6; }
  100% { transform: translateY(0px) translateX(0px) rotate(360deg); opacity: 0.5; }
}

@keyframes particleFloat3 {
  0% { transform: scale(1) translateY(0px); opacity: 0.4; }
  50% { transform: scale(1.6) translateY(-60px); opacity: 0.8; }
  100% { transform: scale(1) translateY(0px); opacity: 0.4; }
}

.results-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.medical-chart, .brain-scan {
  position: absolute;
  opacity: 0.3;
  animation: float 8s ease-in-out infinite;
}

.results-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  z-index: 10;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.results-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px 0;
}

.results-title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 900;
  color: #000;
  margin-bottom: 15px;
  letter-spacing: -0.5px;
}

.results-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  color: #333;
  font-weight: 500;
  letter-spacing: 0.5px;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.5;
}

.results-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* Glassmorphism Card Styles */
.card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(160, 32, 240, 0.1);
  border-radius: 20px;
  padding: clamp(20px, 4vw, 40px);
  box-shadow: 0 8px 32px rgba(160, 32, 240, 0.08);
  position: relative;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card Headers */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(160, 32, 240, 0.1);
}

.card-title {
  font-size: clamp(1.3rem, 2.5vw, 1.6rem);
  font-weight: 800;
  color: #000;
  letter-spacing: -0.5px;
}

/* Confidence Badge */
.confidence-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #a020f0, #8a1acc);
  color: white;
  padding: 12px 24px;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(160, 32, 240, 0.3);
  backdrop-filter: blur(10px);
}

.confidence-label {
  font-size: clamp(0.75rem, 1.2vw, 0.85rem);
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.9;
  font-weight: 600;
}

.confidence-value {
  font-size: clamp(1.2rem, 2vw, 1.5rem);
  font-weight: 900;
  margin-top: 4px;
}

/* Patient Details */
.patient-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  padding: 18px;
  background: rgba(160, 32, 240, 0.05);
  border-radius: 12px;
  border-left: 4px solid #a020f0;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.detail-item:hover {
  background: rgba(160, 32, 240, 0.08);
  transform: translateY(-2px);
}

.detail-label {
  font-size: clamp(0.85rem, 1.5vw, 0.95rem);
  font-weight: 700;
  color: #a020f0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.detail-value {
  font-size: clamp(1rem, 2vw, 1.2rem);
  font-weight: 600;
  color: #000;
  line-height: 1.4;
}

/* Analysis Status */
.analysis-status {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(160, 32, 240, 0.1);
  padding: 10px 18px;
  border-radius: 16px;
  color: #a020f0;
  font-weight: 700;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(160, 32, 240, 0.2);
}

.status-indicator {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #a020f0;
  animation: pulse 2s infinite;
}

.status-indicator.active {
  box-shadow: 0 0 0 0 rgba(160, 32, 240, 0.7);
}

/* Supervisor Analysis Content */
.supervisor-analysis-content {
  padding: 10px 0;
}

.analysis-text-container {
  background: rgba(160, 32, 240, 0.05);
  border-radius: 16px;
  border-left: 4px solid #a020f0;
  backdrop-filter: blur(10px);
  padding: 30px;
  transition: all 0.3s ease;
}

.analysis-text-container:hover {
  background: rgba(160, 32, 240, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(160, 32, 240, 0.1);
}

.analysis-text {
  font-size: clamp(1rem, 2vw, 1.1rem);
  line-height: 1.8;
  color: #333;
  font-weight: 500;
  white-space: pre-line;
  word-wrap: break-word;
  font-family: var(--font-family);
}







/* Notice Card */
.notice-card {
  background: rgba(160, 32, 240, 0.05);
  border: 2px solid rgba(160, 32, 240, 0.2);
  backdrop-filter: blur(20px);
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 20px;
}

.notice-header svg {
  color: #a020f0;
}

.notice-header h3 {
  font-size: clamp(1.2rem, 2.5vw, 1.4rem);
  font-weight: 800;
  color: #a020f0;
  letter-spacing: -0.3px;
}

.notice-content p {
  font-size: clamp(0.95rem, 2vw, 1.1rem);
  line-height: 1.7;
  color: #444;
  font-weight: 500;
}

.notice-content strong {
  color: #a020f0;
  font-weight: 700;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 50px;
  padding-top: 40px;
  border-top: 2px solid rgba(160, 32, 240, 0.1);
}

.action-buttons .btn {
  padding: 18px 40px;
  font-size: clamp(1rem, 2vw, 1.2rem);
  border-radius: 16px;
  letter-spacing: 0.5px;
  font-weight: 700;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.action-buttons .btn-primary {
  background: linear-gradient(135deg, #a020f0, #8a1acc);
  color: white;
  border: none;
  box-shadow: 0 8px 25px rgba(160, 32, 240, 0.3);
}

.action-buttons .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-buttons .btn-primary:hover::before {
  left: 100%;
}

.action-buttons .btn-primary:hover {
  background: linear-gradient(135deg, #8a1acc, #a020f0);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 40px rgba(160, 32, 240, 0.4);
}

.action-buttons .btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #a020f0;
  border: 2px solid #a020f0;
  box-shadow: 0 8px 25px rgba(160, 32, 240, 0.1);
}

.action-buttons .btn-secondary:hover {
  background: #a020f0;
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 40px rgba(160, 32, 240, 0.3);
}

/* Loading State */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
  background: #ffffff;
  position: relative;
}

.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(160, 32, 240, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(160, 32, 240, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 20s linear infinite;
  pointer-events: none;
}

.loading h2 {
  color: #000;
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 800;
  margin-top: 20px;
  text-align: center;
  z-index: 2;
  position: relative;
}

.loading p {
  color: #333;
  font-size: clamp(1rem, 2vw, 1.2rem);
  font-weight: 500;
  margin-top: 10px;
  text-align: center;
  z-index: 2;
  position: relative;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(160, 32, 240, 0.2);
  border-top: 4px solid #a020f0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 2;
  position: relative;
  box-shadow: 0 4px 20px rgba(160, 32, 240, 0.2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .results-container {
    max-width: 900px;
  }

  .patient-details {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .supervisor-results-page {
    padding: 15px;
  }

  .results-header {
    margin-bottom: 30px;
  }

  .patient-details {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .card-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .action-buttons .btn {
    width: 100%;
    max-width: 300px;
  }

  .analysis-text-container {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .supervisor-results-page {
    padding: 10px;
  }

  .card {
    padding: 20px 15px;
    border-radius: 16px;
  }

  .recommendation-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
    padding: 18px;
  }

  .agent-summary {
    flex-direction: column;
    text-align: center;
    gap: 12px;
    padding: 18px;
  }

  .detail-item {
    padding: 15px;
  }
}

@media (max-width: 360px) {
  .supervisor-results-page {
    padding: 8px;
  }

  .card {
    padding: 16px 12px;
    border-radius: 12px;
  }

  .recommendation-item,
  .agent-summary {
    padding: 15px;
  }
}
