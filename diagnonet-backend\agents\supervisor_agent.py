import os, json
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import logging
import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, pipeline

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI router
router = APIRouter()

class AgentOutput(BaseModel):
    """Schema for the vitals agent output only"""
    diagnosis: str = Field(..., description="Primary diagnosis from vitals agent")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0.0-1.0)")
    explanation: str = Field(..., description="Detailed reasoning from vitals agent")

class SupervisorInput(BaseModel):
    """Only vitals output is provided for now"""
    vitals: AgentOutput = Field(...)

class SupervisorOutput(BaseModel):
    """Complete medical analysis output with detailed vitals information"""
    final_diagnosis: str = Field(..., description="Primary medical diagnosis")
    confidence: float = Field(..., description="Confidence score (0-1)")
    model_used: str = Field(..., description="Name of the AI model used")
    reasoning: str = Field(..., description="Detailed medical reasoning")
    agent_agreement: str = Field(..., description="Agent consensus information")
    recommendations: list[str] = Field(..., description="Medical recommendations")
    severity: str = Field(..., description="Severity level (LOW, MODERATE, HIGH, CRITICAL)")
    vitals_details: dict | None = Field(None, description="Detailed vitals analysis breakdown")
    xray_details: dict | None = Field(None, description="X-ray analysis details if available")

class SupervisorAgent:
    """
    Supervisor agent that produces a natural-language briefing
    based solely on the vitals agent output using BioGPT.
    """

    def __init__(self):
        self.model_name = os.getenv("BIOGPT_MODEL", "microsoft/biogpt")
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self._load_model()

    def _load_model(self):
        logger.info(f"Loading BioGPT model: {self.model_name} on {self.device}")
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_name,
            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
            device_map="auto" if self.device == "cuda" else None
        )
        self.generator = pipeline(
            "text-generation",
            model=self.model,
            tokenizer=self.tokenizer,
            device=0 if self.device == "cuda" else -1
        )
        logger.info("BioGPT loaded successfully")

    def analyze(self, payload: SupervisorInput) -> SupervisorOutput:
        try:
            vitals = payload.vitals
            prompt = (
                "You are a senior medical supervisor. "
                "Please provide a concise, human-readable briefing summarizing the vitals analysis below, "
                "highlight key findings, confidence, and next-step recommendations.\n\n"
                f"Diagnosis: {vitals.diagnosis}\n"
                f"Confidence: {vitals.confidence:.2f}\n"
                f"Explanation: {vitals.explanation}\n\n"
                "Briefing:"  
            )
            # Generate summary
            response = self.generator(
                prompt,
                max_length=200,
                temperature=0.2,
                do_sample=False,
                pad_token_id=self.tokenizer.eos_token_id
            )
            text = response[0]["generated_text"].strip()
            # Strip prompt prefix if echoed
            summary = text[len(prompt):].strip() if text.startswith(prompt) else text
            return SupervisorOutput(summary=summary, model_used=self.model_name)
        except Exception as e:
            logger.error(f"SupervisorAgent failed: {e}")
            # Fallback: return raw explanation
            fallback = (
                f"Supervisor fallback summary based on vitals: {payload.vitals.explanation}"
            )
            return SupervisorOutput(summary=fallback, model_used="fallback")

# FastAPI endpoint
@router.post("/supervisor", response_model=SupervisorOutput)
def supervisor_endpoint(input: SupervisorInput):
    agent = SupervisorAgent()
    return agent.analyze(input)
